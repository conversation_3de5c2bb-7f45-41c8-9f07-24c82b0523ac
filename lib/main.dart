import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get_storage/get_storage.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';

import 'package:towasl/core/constants.dart';
import 'package:towasl/core/config/onesignal_notification.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/config/firebase_options.dart';
import 'package:towasl/core/providers/navigation_provider.dart';
import 'package:towasl/core/providers/language_provider.dart';
import 'package:towasl/core/providers/service_providers.dart';
import 'package:towasl/core/routes.dart' as routes;
import 'package:towasl/l10n/app_localizations.dart';
import 'package:towasl/features/splash/presentation/views/splash_view.dart';
import 'package:towasl/features/welcome/presentation/views/welcome_view.dart';
import 'package:towasl/features/authentication/presentation/views/signup_login_view.dart';
import 'package:towasl/features/authentication/presentation/views/mobile_otp_view.dart';
import 'package:towasl/features/interests/presentation/views/interests_view.dart';
import 'package:towasl/features/location/presentation/views/location_view.dart';
import 'package:towasl/features/personal_info/presentation/views/personal_info_view.dart';
import 'package:towasl/features/home/<USER>/views/home_view.dart';
import 'package:towasl/features/profile/presentation/views/profile_view.dart';

/// Application entry point
///
/// Initializes Firebase, OneSignal notifications, and launches the app
Future<void> main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase with platform-specific options
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize Firebase Analytics
  FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);

  // Initialize GetStorage for persistent storage
  await GetStorage.init();

  // Configure OneSignal push notifications
  OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
  OneSignal.initialize(Constants.oneSignalAppID);
  OneSignal.Notifications.requestPermission(true);
  OneSignal.consentGiven(true);

  // Launch the application with Riverpod ProviderScope
  runApp(
    const ProviderScope(
      child: MyApp(),
    ),
  );
}

/// Main application widget
///
/// Root widget that configures the application theme, localization,
/// and notification handlers
class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    // Set up notification click handlers
    OneSignalNotificationsClass.onForegroundNotificationClick();
    OneSignalNotificationsClass.onNotificationClick();

    // Add lifecycle observer
    WidgetsBinding.instance.addObserver(this);

    // Initialize app session for optional update tracking
    _initializeAppSession();

    // Log app open event
    _logAppOpen();
  }

  @override
  void dispose() {
    // Remove lifecycle observer
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Clear session when app is detached (completely closed)
    if (state == AppLifecycleState.detached) {
      _clearAppSession();
    }
  }

  /// Initialize app session for proper optional update tracking
  Future<void> _initializeAppSession() async {
    try {
      final storageService = ref.read(storageServiceProvider);
      await storageService.initializeAppSession();
    } catch (e) {
      // Don't block app startup if this fails
      if (mounted) {
        debugPrint('MyApp: Error initializing app session - $e');
      }
    }
  }

  /// Clear app session when app is properly closed
  Future<void> _clearAppSession() async {
    try {
      final storageService = ref.read(storageServiceProvider);
      await storageService.clearAppSession();
    } catch (e) {
      debugPrint('MyApp: Error clearing app session - $e');
    }
  }

  /// Log app open event for analytics
  Future<void> _logAppOpen() async {
    try {
      final analyticsService = ref.read(analyticsServiceProvider);
      await analyticsService.logAppOpen();
    } catch (e) {
      // Don't block app startup if this fails
      if (mounted) {
        debugPrint('MyApp: Error logging app open - $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch language state for reactive locale changes
    final languageState = ref.watch(languageProvider);

    // SYSTEM CONFIGURATION ----------
    // Set device orientation to portrait only
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Set status bar and system bar color
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: AppColors.primaryPurple,
      statusBarIconBrightness: Brightness.light,
    ));
    // SYSTEM CONFIGURATION ##########

    // APPLICATION CONFIGURATION ----------
    return MaterialApp(
      // Disable debug banner
      debugShowCheckedModeBanner: false,

      // App title
      title: 'Towasl',

      // Navigation key for Riverpod navigation provider
      navigatorKey: Navigation.navigatorKey,

      // Localization configuration
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales: AppLocalizations.supportedLocales,
      locale: languageState.locale,

      // Theme configuration
      theme: ThemeData(
        useMaterial3: true,

        // Scrollbar appearance
        scrollbarTheme: ScrollbarThemeData(
          thumbColor: WidgetStateProperty.all(AppColors.greyMedium),
        ),

        // AppBar theme
        appBarTheme: const AppBarTheme(
          backgroundColor: AppColors.primaryPurple,
          iconTheme: IconThemeData(color: AppColors.whitePure),
          titleTextStyle: TextStyle(
            color: AppColors.whitePure,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // Initial screen
      home: const SplashView(),

      // Route generation for dynamic routes
      onGenerateRoute: _generateRoute,

      // Handle unknown routes
      onUnknownRoute: (settings) {
        return MaterialPageRoute(
          builder: (context) => const SplashView(),
        );
      },
    );
    // APPLICATION CONFIGURATION ##########
  }

  /// Generate routes for navigation
  Route<dynamic>? _generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case routes.AppRoutes.splash:
        return MaterialPageRoute(builder: (_) => const SplashView());
      case routes.AppRoutes.welcome:
        return MaterialPageRoute(builder: (_) => const WelcomeView());
      case routes.AppRoutes.signupLogin:
        return MaterialPageRoute(builder: (_) => const SignupLoginView());
      case routes.AppRoutes.mobileOtp:
        // Handle OTP route with parameters
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => MobileOtpView(
            mobileNumber: args?['mobileNumber'] ?? '',
            countryCode: args?['countryCode'] ?? '966',
          ),
        );
      case routes.AppRoutes.interests:
        return MaterialPageRoute(builder: (_) => const InterestsView());
      case routes.AppRoutes.location:
        return MaterialPageRoute(builder: (_) => const LocationView());
      case routes.AppRoutes.personalInfo:
        return MaterialPageRoute(builder: (_) => const PersonalInfoView());
      case routes.AppRoutes.home:
        return MaterialPageRoute(builder: (_) => const HomeView());
      case routes.AppRoutes.profile:
        return MaterialPageRoute(builder: (_) => const ProfileView());
      default:
        return null;
    }
  }
}
